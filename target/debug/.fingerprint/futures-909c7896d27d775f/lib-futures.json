{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 336243669335521001, "path": 8256309181481024205, "deps": [[5103565458935487, "futures_io", false, 17223198152638510395], [1811549171721445101, "futures_channel", false, 2809131337950437782], [7013762810557009322, "futures_sink", false, 1398383844577758075], [7620660491849607393, "futures_core", false, 12134165656439499170], [10629569228670356391, "futures_util", false, 13514573867532035951], [12779779637805422465, "futures_executor", false, 12557530104660322443], [16240732885093539806, "futures_task", false, 10963430061173711135]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-909c7896d27d775f/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
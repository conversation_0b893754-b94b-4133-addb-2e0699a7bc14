//! Live Pump.fun Integration for Real-Time Trading
//!
//! This module provides complete integration with live pump.fun feeds:
//! - Real-time WebSocket monitoring of new token creations
//! - Live price feeds and market data
//! - Pump.fun program interaction for actual trading
//! - Real-time position monitoring and management

use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    instruction::Instruction,
    system_instruction,
    commitment_config::CommitmentConfig,
};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};
use std::{
    time::{SystemTime, UNIX_EPOCH, Duration, Instant},
    str::FromStr,
    sync::Arc,
    env,
};
use tokio::{
    time::sleep,
    sync::Mutex,
};
use rand;
// use futures_util::{SinkExt, StreamExt}; // Disabled for performance
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};
use crate::production_jito::{ProductionJitoClient, TradingPriority};
use crate::performance_dashboard::PerformanceDashboard;
// use crate::web_dashboard::WebDashboard; // Disabled for performance
use crate::pump_instruction_builder::{PumpInstructionBuilder, get_bonding_curve_state, BondingCurveAccount};
use crate::priority_fees_executor::{PriorityFeesExecutor, PriorityFeesConfig, TransactionResult};
use crate::pump_api_client::PumpApiClient;
use crate::position_state_machine::{PositionState, ExitReason};
use crate::position_manager::{PositionManager, ManagedPosition};
use crate::live_pump_integration::{DipMonitor, EnhancedStateMachine};

// Pump.fun program constants
pub const PUMP_PROGRAM_ID: &str = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
pub const PUMP_GLOBAL_ACCOUNT: &str = "4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf";
pub const PUMP_FEE_RECIPIENT: &str = "7VtfL8fvgNfhz17qKRMjzQEXgbdpnHHHQRh54R9jP2RJ";

// Pump.fun API endpoints
pub const PUMP_API_BASE: &str = "https://frontend-api.pump.fun";
pub const PUMP_WS_ENDPOINT: &str = "wss://pumpportal.fun/api/data";

/// Configuration loaded from environment variables
#[derive(Debug, Clone)]
pub struct TradingConfig {
    // Token filtering
    pub min_market_cap_usd: f64,
    pub max_market_cap_usd: f64,
    pub min_liquidity_sol: f64,
    pub max_liquidity_sol: f64,
    pub min_token_age_seconds: u64,
    pub max_token_age_seconds: u64,
    pub min_holders: u32,
    pub max_holders: u32,
    pub min_token_name_length: usize,
    pub max_token_name_length: usize,

    // Blocked words
    pub blocked_words: Vec<String>,

    // Position sizing
    pub base_position_percent: f64,
    pub min_trade_amount: f64,
    pub max_trade_amount: f64,

    // Timing
    pub bonding_curve_wait_seconds: u64,
    pub position_monitor_interval_seconds: u64,

    // Trading parameters
    pub sol_to_usd_rate: f64,
    pub test_sol_amount: f64,
    pub starting_capital_sol: f64,
}

impl TradingConfig {
    pub fn from_env() -> Self {
        Self {
            min_market_cap_usd: env::var("PUMP_MIN_MARKET_CAP_USD")
                .unwrap_or_else(|_| "3000".to_string())
                .parse()
                .unwrap_or(3000.0),
            max_market_cap_usd: env::var("PUMP_MAX_MARKET_CAP_USD")
                .unwrap_or_else(|_| "5000000".to_string())
                .parse()
                .unwrap_or(5000000.0),
            min_liquidity_sol: env::var("PUMP_MIN_LIQUIDITY_SOL")
                .unwrap_or_else(|_| "10.0".to_string())
                .parse()
                .unwrap_or(10.0),
            max_liquidity_sol: env::var("PUMP_MAX_LIQUIDITY_SOL")
                .unwrap_or_else(|_| "1000.0".to_string())
                .parse()
                .unwrap_or(1000.0),
            min_token_age_seconds: env::var("PUMP_MIN_TOKEN_AGE_SECONDS")
                .unwrap_or_else(|_| "0".to_string())
                .parse()
                .unwrap_or(0),
            max_token_age_seconds: env::var("PUMP_MAX_TOKEN_AGE_SECONDS")
                .unwrap_or_else(|_| "1800".to_string())
                .parse()
                .unwrap_or(1800),
            min_holders: env::var("PUMP_MIN_HOLDERS")
                .unwrap_or_else(|_| "1".to_string())
                .parse()
                .unwrap_or(1),
            max_holders: env::var("PUMP_MAX_HOLDERS")
                .unwrap_or_else(|_| "10000".to_string())
                .parse()
                .unwrap_or(10000),
            min_token_name_length: env::var("PUMP_MIN_TOKEN_NAME_LENGTH")
                .unwrap_or_else(|_| "4".to_string())
                .parse()
                .unwrap_or(4),
            max_token_name_length: env::var("PUMP_MAX_TOKEN_NAME_LENGTH")
                .unwrap_or_else(|_| "25".to_string())
                .parse()
                .unwrap_or(25),
            blocked_words: env::var("PUMP_BLOCKED_WORDS")
                .unwrap_or_else(|_| "scam,fake,test,rug,honeypot,ponzi,dump".to_string())
                .split(',')
                .map(|s| s.trim().to_lowercase())
                .collect(),
            base_position_percent: env::var("PUMP_BASE_POSITION_PERCENT")
                .unwrap_or_else(|_| "10.0".to_string())
                .parse()
                .unwrap_or(10.0),
            min_trade_amount: env::var("PUMP_MIN_TRADE_AMOUNT")
                .unwrap_or_else(|_| "0.0001".to_string())
                .parse()
                .unwrap_or(0.0001),
            max_trade_amount: env::var("PUMP_MAX_TRADE_AMOUNT")
                .unwrap_or_else(|_| "0.005".to_string())
                .parse()
                .unwrap_or(0.005),
            bonding_curve_wait_seconds: env::var("PUMP_BONDING_CURVE_WAIT_SECONDS")
                .unwrap_or_else(|_| "2".to_string())
                .parse()
                .unwrap_or(2),
            position_monitor_interval_seconds: env::var("PUMP_POSITION_MONITOR_INTERVAL_SECONDS")
                .unwrap_or_else(|_| "2".to_string())
                .parse()
                .unwrap_or(2),
            sol_to_usd_rate: env::var("PUMP_SOL_TO_USD_RATE")
                .unwrap_or_else(|_| "100.0".to_string())
                .parse()
                .unwrap_or(100.0),
            test_sol_amount: env::var("PUMP_TEST_SOL_AMOUNT")
                .unwrap_or_else(|_| "0.001".to_string())
                .parse()
                .unwrap_or(0.001),
            starting_capital_sol: env::var("PUMP_STARTING_CAPITAL_SOL")
                .unwrap_or_else(|_| "0.025".to_string())
                .parse()
                .unwrap_or(0.025),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiveTokenData {
    pub mint: String,
    pub name: String,
    pub symbol: String,
    pub description: String,
    pub image: String,
    pub creator: String,
    pub creation_time: u64,
    pub market_cap_usd: f64,
    pub liquidity_sol: f64,
    pub price_sol: f64,
    pub volume_24h: f64,
    pub holders: u32,
    pub transactions: u32,
    pub is_complete: bool,
    pub bonding_curve: String,
    pub associated_bonding_curve: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PumpFunWebSocketMessage {
    #[serde(rename = "type")]
    pub message_type: String,
    pub data: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenCreationEvent {
    pub mint: String,
    pub name: String,
    pub symbol: String,
    pub uri: String,
    #[serde(rename = "traderPublicKey")]
    pub creator: String,
    #[serde(rename = "bondingCurveKey")]
    pub bonding_curve: String,
    #[serde(rename = "marketCapSol")]
    pub market_cap_sol: f64,
    #[serde(rename = "vSolInBondingCurve")]
    pub liquidity_sol: f64,
    #[serde(rename = "solAmount")]
    pub initial_buy_sol: f64,
    #[serde(rename = "initialBuy")]
    pub initial_buy_tokens: f64,
    pub signature: String,
    #[serde(rename = "txType")]
    pub tx_type: String,

    // Computed fields for compatibility
    #[serde(skip)]
    pub timestamp: u64,
    #[serde(skip)]
    pub initial_liquidity: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeEvent {
    pub mint: String,
    pub trader: String,
    pub side: String, // "buy" or "sell"
    pub amount_sol: f64,
    pub amount_tokens: u64,
    pub price: f64,
    pub timestamp: u64,
    pub signature: String,
}

// REMOVED: Old LivePosition struct replaced by state machine
// Position management now handled by PositionManager with proper state machine

pub struct LivePumpFunClient {
    rpc_client: RpcClient,
    http_client: Client,
    wallet_keypair: Keypair,

    // Trading configuration from environment variables
    trading_config: TradingConfig,
    take_profit_percent: f64,
    stop_loss_percent: f64,
    max_holding_time: Duration,

    // Professional trailing stop loss
    trailing_stop_percent: f64,
    use_trailing_stop: bool,

    // Professional kill switches
    max_pnl_percent: f64,
    max_consecutive_losses: u32,
    enable_kill_switches: bool,
    consecutive_losses: u32,
    session_active: bool,

    // "Sperm and Egg" model - pause new token listening while positions are active
    pause_listening_when_trading: bool,

    // Production Jito client for MEV protection
    jito_client: ProductionJitoClient,

    // Priority Fees Execution
    priority_executor: Option<PriorityFeesExecutor>,
    pump_instruction_builder: PumpInstructionBuilder,
    pump_api_client: PumpApiClient,

    // Program accounts
    pump_program_id: Pubkey,
    pump_global_account: Pubkey,
    pump_fee_recipient: Pubkey,

    // Unified Position Management with State Machine
    position_manager: PositionManager,

    // Performance tracking
    total_trades: u32,
    successful_trades: u32,
    total_pnl: f64,

    // Extracted scanning modules
    token_scanner: crate::live_pump_integration::TokenScanner,
    periodic_scanner: crate::live_pump_integration::PeriodicScanner,

    // Extracted trading modules
    trade_executor: crate::live_pump_integration::TradeExecutor,
    position_monitor: crate::live_pump_integration::PositionMonitor,
    sell_logic: crate::live_pump_integration::SellLogic,

    // Performance Dashboard
    dashboard: PerformanceDashboard,

    // Web Dashboard with Bonding Curve Visualization (DISABLED for performance)
    // web_dashboard: WebDashboard,

    // Enhanced State Machine with Dip-Buy Support
    pub enhanced_state_machine: crate::live_pump_integration::EnhancedStateMachine,

    // NEW: Bonding Curve Monitoring System (Chainstack-inspired)
    bonding_curve_monitor: crate::live_pump_integration::BondingCurveMonitor,

    // Concurrent Trading Engine Components
    aging_pipeline_handle: Option<tokio::task::JoinHandle<()>>,
    trade_coordinator: Arc<TokioMutex<TradeCoordinator>>,
}

use crate::live_pump_integration::handoff_fix;

// Concurrent trading components
use tokio::sync::{mpsc, Mutex as TokioMutex};

/// Coordinates trading operations and manages process lifecycle
#[derive(Debug)]
pub struct TradeCoordinator {
    pub is_trading_active: bool,
    pub current_trade_symbol: Option<String>,
    pub should_pause_new_tokens: bool,
    pub should_purge_aging_pipeline: bool,
}

impl TradeCoordinator {
    pub fn new() -> Self {
        Self {
            is_trading_active: false,
            current_trade_symbol: None,
            should_pause_new_tokens: false,
            should_purge_aging_pipeline: false,
        }
    }

    pub fn start_trading(&mut self, symbol: String) {
        self.is_trading_active = true;
        self.current_trade_symbol = Some(symbol);
        self.should_pause_new_tokens = true;
        println!("🔒 TRADE COORDINATOR: Trading started - pausing other processes");
    }

    pub fn end_trading(&mut self, purge_aging_pipeline: bool) {
        self.is_trading_active = false;
        self.current_trade_symbol = None;
        self.should_pause_new_tokens = false;
        self.should_purge_aging_pipeline = purge_aging_pipeline;
        println!("🔓 TRADE COORDINATOR: Trading ended - resuming processes");
    }

    pub fn should_accept_new_tokens(&self) -> bool {
        !self.should_pause_new_tokens && !self.is_trading_active
    }
}

impl LivePumpFunClient {
    /// Fetch the fee recipient from the pump.fun global account
    async fn fetch_fee_recipient_from_global_account(rpc_client: &RpcClient) -> Result<Pubkey> {
        let global_account_pda = Pubkey::find_program_address(
            &[b"global"],
            &Pubkey::from_str(PUMP_PROGRAM_ID)?,
        ).0;

        println!("🔍 Fetching fee recipient from global account: {}", global_account_pda);

        let account_info = rpc_client.get_account(&global_account_pda)
            .map_err(|e| anyhow!("Failed to fetch global account: {}", e))?;

        // The global account structure (based on the official SDK):
        // - discriminator: 8 bytes
        // - initialized: 1 byte
        // - authority: 32 bytes
        // - feeRecipient: 32 bytes (starts at offset 41)
        if account_info.data.len() < 73 {
            return Err(anyhow!("Global account data too short: {} bytes", account_info.data.len()));
        }

        let fee_recipient_bytes = &account_info.data[41..73];
        let fee_recipient = Pubkey::try_from(fee_recipient_bytes)
            .map_err(|e| anyhow!("Failed to parse fee recipient: {}", e))?;

        println!("✅ Fee recipient fetched from global account: {}", fee_recipient);
        Ok(fee_recipient)
    }

    pub async fn new(
        rpc_client: RpcClient,
        wallet_keypair: Keypair,
        take_profit_percent: f64,
        stop_loss_percent: f64,
        max_holding_time_minutes: u64,
        trailing_stop_percent: f64,
        use_trailing_stop: bool,
        max_pnl_percent: f64,
        max_consecutive_losses: u32,
        enable_kill_switches: bool,
        pause_listening_when_trading: bool,
    ) -> Result<Self> {
        let pump_program_id = Pubkey::from_str(PUMP_PROGRAM_ID)?;
        let pump_global_account = Pubkey::from_str(PUMP_GLOBAL_ACCOUNT)?;

        // Fetch fee recipient dynamically from the global account
        let pump_fee_recipient = Self::fetch_fee_recipient_from_global_account(&rpc_client).await?;

        // Note: Jito client initialization removed - using priority fees only
        let jito_client = ProductionJitoClient::new(
            RpcClient::new_with_commitment(&rpc_client.url(), rpc_client.commitment()), // Reuse main RPC
            0,   // disabled
            0,   // disabled
        )?;

        // Initialize priority fees executor (reuse main RPC client)
        let priority_config = PriorityFeesConfig::from_env();
        let priority_executor = Some(PriorityFeesExecutor::new(
            RpcClient::new_with_commitment(&rpc_client.url(), rpc_client.commitment()),
            priority_config,
        ));

        // Initialize pump instruction builder and API client
        let mut pump_instruction_builder = PumpInstructionBuilder::new()?;
        pump_instruction_builder.set_fee_recipient(pump_fee_recipient);
        let pump_api_client = PumpApiClient::new();

        // Load trading configuration from environment variables
        let trading_config = TradingConfig::from_env();

        // Debug: Print loaded configuration
        println!("🔧 TRADING CONFIGURATION LOADED:");
        println!("   Min Token Age: {} seconds", trading_config.min_token_age_seconds);
        println!("   Max Token Age: {} seconds", trading_config.max_token_age_seconds);
        println!("   Min Liquidity: {} SOL", trading_config.min_liquidity_sol);
        println!("   Min Market Cap: ${:.0}", trading_config.min_market_cap_usd);
        println!("   Max Market Cap: ${:.0}", trading_config.max_market_cap_usd);

        // Print scanning configuration
        crate::live_pump_integration::TokenScanner::print_scanning_config();

        // Initialize unified position manager with state machine (single instance)
        let shared_position_manager = PositionManager::new();

        // Clone values for module initialization
        let rpc_url = rpc_client.url().to_string();
        let rpc_commitment = rpc_client.commitment();
        let wallet_keypair_bytes = wallet_keypair.to_bytes();

        // Create shared RPC clients for modules (reduce connection overhead)
        let shared_rpc_client = RpcClient::new_with_commitment(&rpc_url, rpc_commitment);
        let shared_rpc_client_2 = RpcClient::new_with_commitment(&rpc_url, rpc_commitment);
        let shared_rpc_client_3 = RpcClient::new_with_commitment(&rpc_url, rpc_commitment);

        // Initialize dip monitor and enhanced state machine
        let dip_monitor = DipMonitor::new(shared_rpc_client);
        let enhanced_state_machine = EnhancedStateMachine::new(dip_monitor);

        // Initialize bonding curve monitor (Chainstack-inspired approach)
        let bonding_curve_monitor = crate::live_pump_integration::BondingCurveMonitor::new(
            shared_rpc_client_2
        );

        // Initialize trade coordinator
        let trade_coordinator = Arc::new(TokioMutex::new(TradeCoordinator::new()));

        Ok(Self {
            rpc_client,
            http_client: Client::new(),
            wallet_keypair,
            trading_config: trading_config.clone(),
            take_profit_percent,
            stop_loss_percent,
            max_holding_time: Duration::from_secs(max_holding_time_minutes * 60),
            trailing_stop_percent,
            use_trailing_stop,
            max_pnl_percent,
            max_consecutive_losses,
            enable_kill_switches,
            consecutive_losses: 0,
            session_active: true,
            pause_listening_when_trading,
            jito_client,
            priority_executor,
            pump_instruction_builder,
            pump_api_client,
            pump_program_id,
            pump_global_account,
            pump_fee_recipient,
            position_manager: shared_position_manager,
            total_trades: 0,
            successful_trades: 0,
            total_pnl: 0.0,

            // Initialize extracted scanning modules with RPC client for IDL-based scanning
            token_scanner: {
                let rpc_url = std::env::var("PUMP_RPC_URL")
                    .expect("PUMP_RPC_URL environment variable must be set");
                crate::live_pump_integration::TokenScanner::new().with_rpc_client(rpc_url)
            },
            periodic_scanner: crate::live_pump_integration::PeriodicScanner::new(),

            // Initialize extracted trading modules using shared clients
            trade_executor: {
                let mut trade_instruction_builder = PumpInstructionBuilder::new()?;
                trade_instruction_builder.set_fee_recipient(pump_fee_recipient);
                crate::live_pump_integration::TradeExecutor::new(
                    shared_rpc_client_3,
                    Keypair::from_bytes(&wallet_keypair_bytes)?,
                    trading_config.clone(),
                    Some(PriorityFeesExecutor::new(
                        RpcClient::new_with_commitment(&rpc_url, rpc_commitment),
                        PriorityFeesConfig::from_env(),
                    )),
                    ProductionJitoClient::new(
                        RpcClient::new_with_commitment(&rpc_url, rpc_commitment),
                        0, 0,
                    )?,
                    trade_instruction_builder,
                    pump_fee_recipient,
                )
            },
            position_monitor: {
                let mut monitor_instruction_builder = PumpInstructionBuilder::new()?;
                monitor_instruction_builder.set_fee_recipient(pump_fee_recipient);
                crate::live_pump_integration::PositionMonitor::new(
                    PositionManager::new(), // Create separate instance for monitor
                    trading_config.clone(),
                    RpcClient::new_with_commitment(&rpc_url, rpc_commitment),
                    monitor_instruction_builder,
                )
            },
            sell_logic: crate::live_pump_integration::SellLogic::new(trading_config),
            dashboard: PerformanceDashboard::new(),
            // web_dashboard: WebDashboard::new(), // DISABLED for performance
            enhanced_state_machine,
            bonding_curve_monitor,
            aging_pipeline_handle: None,
            trade_coordinator,
        })
    }

    /// Start concurrent aging pipeline for continuous background monitoring
    async fn start_concurrent_aging_pipeline(&mut self) {
        println!("🚀 STARTING CONCURRENT AGING PIPELINE");
        println!("   🔄 Background process: Continuous graduation candidate monitoring");
        println!("   ⚡ Performance: Independent of main trading loop");
        println!("   🎯 Strategy: Always scanning for opportunities");

        // Instead of cloning, we'll use the existing continuous aging pipeline
        // and coordinate through the trade coordinator
        let trade_coordinator = Arc::clone(&self.trade_coordinator);

        let handle = tokio::spawn(async move {
            let mut scan_counter = 0u64;
            let scan_interval = Duration::from_secs(30); // Scan every 30 seconds

            loop {
                scan_counter += 1;

                // Check if trading is active - if so, reduce scan frequency
                let should_scan = {
                    let coordinator = trade_coordinator.lock().await;
                    if coordinator.is_trading_active {
                        // Reduce frequency during active trading
                        scan_counter % 4 == 0 // Scan every 2 minutes during trading
                    } else {
                        true // Normal frequency when not trading
                    }
                };

                if should_scan {
                    // Log background monitoring status
                    if scan_counter % 20 == 0 {
                        // Log status every 10 minutes (20 * 30s)
                        println!("🔄 AGING PIPELINE: Background monitoring active (scan #{}) - main loop handles scanning", scan_counter);
                    }
                }

                // Check if we should purge the pipeline
                {
                    let mut coordinator = trade_coordinator.lock().await;
                    if coordinator.should_purge_aging_pipeline {
                        println!("🧹 AGING PIPELINE: Purge signal received after successful trade");
                        // Note: Actual purging would be implemented in bonding_curve_monitor
                        coordinator.should_purge_aging_pipeline = false;
                    }
                }

                tokio::time::sleep(scan_interval).await;
            }
        });

        self.aging_pipeline_handle = Some(handle);
        println!("✅ Concurrent aging pipeline started successfully!");
    }

    /// Start live pump.fun monitoring and trading
    pub async fn start_live_trading(&mut self) -> Result<()> {
        println!("🚀 STARTING LIVE PUMP.FUN TRADING");
        println!("================================");
        println!("🔗 Connecting to live pump.fun feeds...");
        println!("📊 Take Profit: +{:.1}%", self.take_profit_percent);
        println!("🛡️  Stop Loss: -{:.1}%", self.stop_loss_percent);
        if self.use_trailing_stop {
            println!("📈 PROFESSIONAL TRAILING STOP: -{:.1}% from peak", self.trailing_stop_percent);
            println!("💡 Will sell at -{:.1}% from highest price reached", self.trailing_stop_percent);
        }
        println!("⏰ Max Hold Time: {} minutes", self.max_holding_time.as_secs() / 60);

        if self.enable_kill_switches {
            println!();
            println!("🚨 PROFESSIONAL KILL SWITCHES ACTIVE:");
            println!("   🎯 Max P&L: +{:.0}% (session ends at profit target)", self.max_pnl_percent);
            println!("   🛡️  Max Consecutive Losses: {} (session ends for protection)", self.max_consecutive_losses);
            println!("   ⚠️  Session will auto-terminate when either limit is reached");
        }

        if self.pause_listening_when_trading {
            println!();
            println!("🥚 SPERM AND EGG MODEL ACTIVE:");
            println!("   🔇 New token listening will PAUSE when positions are active");
            println!("   🎯 Focus on one trade at a time for maximum precision");
            println!("   🔊 Listening will RESUME when all positions are closed");
        }

        // Print bonding curve monitor configuration
        self.bonding_curve_monitor.print_configuration();

        // Start concurrent aging pipeline for continuous background monitoring
        self.start_concurrent_aging_pipeline().await;

        // Dashboard disabled for performance optimization
        println!("🚫 Web dashboard DISABLED for performance optimization");
        println!("💡 Focus on core trading functionality - dashboard can be re-enabled later");

        // Print dip-buy configuration if enabled
        if DipMonitor::is_enabled() {
            println!();
            println!("🎯 DIP-BUY FEATURE ENABLED:");
            println!("   📉 Dip Trigger: {}%",
                std::env::var("PUMP_DIP_PERCENTAGE").unwrap_or_else(|_| "5.0".to_string()));
            println!("   ⏱️  Stabilization: {}s",
                std::env::var("PUMP_STABILIZATION_SECONDS").unwrap_or_else(|_| "15".to_string()));
            println!("   ⏰ Max Watch Time: {}s",
                std::env::var("PUMP_DIP_MONITORING_SECONDS").unwrap_or_else(|_| "300".to_string()));
            println!("   👁️  Max Watched Tokens: {}",
                std::env::var("PUMP_MAX_WATCHED_TOKENS").unwrap_or_else(|_| "3".to_string()));
            println!("   💡 Strategy: Monitor EXISTING tokens (600+ seconds old) for price dips");
            println!("   🔄 Will periodically scan for established tokens, not just new ones");
        }
        println!();

        // Always attempt initial token scan with patience and accuracy
        println!("🔍 SCANNING EXISTING LIVE TOKENS...");
        println!("   💡 Taking time to ensure we don't miss trading opportunities");
        println!("   🕐 This may take a few minutes during high API load - being patient for accuracy");

        match self.token_scanner.scan_existing_tokens().await {
            Ok(()) => {
                println!("✅ Successfully scanned existing tokens!");
            }
            Err(e) => {
                println!("⚠️  API token scan failed: {}", e);
                println!("🔍 FALLBACK: Trying Chainstack IDL-based bonding curve scan...");

                // Try IDL-based scanning as fallback with ultra-conservative batch sizes
                let batch_configs = vec![
                    (3, 5),   // 3 accounts, 5 second delay
                    (2, 8),   // 2 accounts, 8 second delay
                    (1, 12),  // 1 account, 12 second delay
                ];
                let mut idl_success = false;

                println!("🔄 ULTRA-CONSERVATIVE IDL APPROACH: Using minimal batch sizes with extended delays");

                for (attempt, &(batch_size, delay_secs)) in batch_configs.iter().enumerate() {
                    println!("🔄 IDL attempt {} with batch size: {} ({}s delay)",
                        attempt + 1, batch_size, delay_secs);

                    match self.token_scanner.scan_existing_tokens_via_idl_with_batch_size(batch_size).await {
                        Ok(qualifying_tokens) => {
                            println!("✅ IDL scan successful! Found {} qualifying tokens", qualifying_tokens);
                            if qualifying_tokens > 0 {
                                println!("🎯 Ready to trade with IDL-discovered tokens!");
                            } else {
                                println!("📊 No qualifying tokens found, but scan completed successfully");
                            }
                            idl_success = true;
                            break;
                        }
                        Err(idl_error) => {
                            let error_msg = idl_error.to_string();
                            if error_msg.contains("deprioritized") && attempt < batch_configs.len() - 1 {
                                println!("⚠️  Batch size {} still too large for current RPC load", batch_size);
                                println!("   ⏳ Waiting {}s before trying even smaller batch...", delay_secs);
                                tokio::time::sleep(tokio::time::Duration::from_secs(delay_secs)).await;
                                continue;
                            } else {
                                println!("⚠️  IDL scan failed: {}", idl_error);
                                break;
                            }
                        }
                    }
                }

                if !idl_success {
                    println!("   ➡️  All ultra-conservative IDL attempts failed");
                    println!("   💡 RPC load is extremely high - this is normal during peak trading");
                    println!("   🔄 Will continue with WebSocket monitoring for new tokens...");
                }
            }
        }

        // Create channels for communication
        let (token_tx, mut token_rx) = mpsc::channel::<TokenCreationEvent>(100);
        let (trade_tx, mut trade_rx) = mpsc::channel::<TradeEvent>(1000);

        // Skip WebSocket monitoring - using bonding curve analysis strategy instead
        println!("⚠️  WebSocket monitoring disabled - using bonding curve analysis strategy");
        println!("💡 This avoids PumpPortal/Chainstack protocol conflicts");

        // Keep channels for compatibility but don't start WebSocket
        let _ws_handle = tokio::spawn(async move {
            println!("🔄 WebSocket monitoring task disabled (bonding curve strategy active)");
            // Keep channels alive but don't actually monitor
            tokio::time::sleep(tokio::time::Duration::from_secs(u64::MAX)).await;
        });

        // REMOVED: Old shared state system replaced by unified PositionManager
        // Position monitoring now handled by state machine with proper validation

        // REMOVED: Old background position monitoring task
        // Position monitoring now integrated into main loop with state machine

        // Main trading loop with unified position management
        let mut last_periodic_scan = std::time::Instant::now();
        let periodic_scan_interval = std::time::Duration::from_secs(120); // 2 minutes (optimized for fresh token tracking)

        loop {
            // Check kill switches before processing any events
            if self.enable_kill_switches && !self.session_active {
                println!("🚨 SESSION TERMINATED BY KILL SWITCH!");
                break;
            }

            tokio::select! {
                // Handle new token creations
                Some(token_event) = token_rx.recv() => {
                    if self.session_active {
                        // Check trade coordinator for listening permission
                        let should_accept_tokens = {
                            let coordinator = self.trade_coordinator.lock().await;
                            coordinator.should_accept_new_tokens()
                        };

                        if !should_accept_tokens {
                            println!("🔇 TRADE COORDINATOR: Ignoring new token {} - trading active", token_event.symbol);
                            continue;
                        }

                        // Check enhanced state machine for listening permission
                        if !self.enhanced_state_machine.current_state().should_listen_for_new_tokens() {
                            println!("🔇 STATE MACHINE: Ignoring new token {} - State: {}",
                                token_event.symbol,
                                self.enhanced_state_machine.current_state().description());
                            continue;
                        }

                        if let Err(e) = self.handle_new_token_with_dip_support(token_event).await {
                            eprintln!("❌ Failed to handle new token: {}", e);
                        }

                        self.check_kill_switches().await;
                    }
                }

                // Handle trade events for position price updates
                Some(trade_event) = trade_rx.recv() => {
                    if self.session_active {
                        // Update position prices via state machine
                        if let Err(e) = self.position_manager.update_position_price(&trade_event.mint, trade_event.price).await {
                            eprintln!("⚠️ Failed to update position price: {}", e);
                        }
                        self.check_kill_switches().await;
                    }
                }

                // Periodic position monitoring with state machine
                _ = tokio::time::sleep(Duration::from_secs(self.trading_config.position_monitor_interval_seconds)) => {
                    if self.session_active {
                        // Check if we have active positions (Sperm and Egg model)
                        let has_active_positions = self.position_manager.has_active_positions().await;

                        if has_active_positions {
                            // ACTIVE TRADE: Focus only on position monitoring using extracted module
                            match self.position_monitor.monitor_all_positions().await {
                                Ok(monitoring_result) => {
                                    // Process any positions that need to be exited
                                    for (token_mint, exit_reason, token_amount) in monitoring_result.positions_to_exit {
                                        println!("🚨 SELL TRIGGER: {} - {} ({} tokens)", token_mint, exit_reason.description(), token_amount);

                                        // Execute sell order using trade executor with token amount
                                        match self.trade_executor.execute_sell_for_token(&token_mint, exit_reason.clone(), token_amount).await {
                                            Ok(sell_result) => {
                                                println!("✅ Sell executed: {} SOL received", sell_result.amount_sol_received);

                                                // Update dashboard status to sold
                                                // Dashboard disabled for performance
                                                // let status = match exit_reason { ... };
                                                // self.update_token_dashboard_status(&token_mint, status).await;

                                                // Remove position from manager
                                                if let None = self.position_manager.remove_position(&token_mint).await {
                                                    println!("⚠️  Position not found for removal");
                                                }

                                                // Update statistics
                                                self.total_trades += 1;
                                                if sell_result.amount_sol_received > 0.0 {
                                                    self.successful_trades += 1;
                                                }

                                                // Notify enhanced state machine of trade completion
                                                self.enhanced_state_machine.end_trading();

                                                // Notify trade coordinator of successful trade completion
                                                {
                                                    let mut coordinator = self.trade_coordinator.lock().await;
                                                    coordinator.end_trading(true); // Purge aging pipeline after successful sale
                                                }
                                            }
                                            Err(e) => {
                                                println!("❌ Sell execution failed: {}", e);
                                            }
                                        }
                                    }
                                }
                                Err(e) => {
                                    println!("⚠️  Position monitoring failed: {}", e);
                                }
                            }
                        } else {
                            // NO ACTIVE TRADES: Safe to do periodic token scanning and dip monitoring
                            self.periodic_scanner.periodic_token_scan(&self.position_manager).await;

                            // Check bonding curve monitor for graduation candidates (continuous)
                            match self.bonding_curve_monitor.scan_for_opportunities().await {
                                Ok(opportunities) => {
                                    if !opportunities.is_empty() {
                                        println!("🎓 BONDING CURVE MONITOR: {} graduation candidates found!", opportunities.len());

                                        for opportunity in opportunities {
                                            println!("🚀 GRADUATION CANDIDATE: {} (liquidity: {:.1} SOL, mcap: ${:.0})",
                                                opportunity.name,
                                                opportunity.liquidity_sol,
                                                opportunity.market_cap_usd);

                                            // Dashboard disabled for performance
                                            // self.update_token_dashboard_with_bonding_curve(...).await;

                                            // Execute buy order for graduation candidate with coordination
                                            {
                                                let mut coordinator = self.trade_coordinator.lock().await;
                                                coordinator.start_trading(opportunity.symbol.clone());
                                            }
                                            self.enhanced_state_machine.start_trading(opportunity.symbol.clone());

                                            if let Err(e) = self.execute_graduation_candidate_buy(&opportunity).await {
                                                eprintln!("❌ Failed to execute graduation candidate buy: {}", e);
                                                // Update dashboard status to failed
                                                // self.update_token_dashboard_status(&opportunity.mint, "failed").await;
                                                self.enhanced_state_machine.end_trading();
                                                {
                                                    let mut coordinator = self.trade_coordinator.lock().await;
                                                    coordinator.end_trading(false); // Don't purge on failure
                                                }
                                            }
                                        }
                                    } else {
                                        // Log aging pipeline status every 10 cycles (reduce spam)
                                        static mut LOG_COUNTER: u32 = 0;
                                        unsafe {
                                            LOG_COUNTER += 1;
                                            if LOG_COUNTER % 10 == 0 {
                                                println!("🔄 Aging pipeline active - monitoring fresh tokens for graduation potential");
                                            }
                                        }
                                    }
                                }
                                Err(e) => {
                                    println!("⚠️  Bonding curve monitor scan failed: {}", e);
                                }
                            }

                            // Check for dip opportunities if dip-buy is enabled
                            if DipMonitor::is_enabled() {
                                match self.enhanced_state_machine.check_dip_opportunities().await {
                                    Ok(tokens_to_buy) => {
                                        for token_data in tokens_to_buy {
                                            println!("🎯 DIP OPPORTUNITY: Executing buy for {}", token_data.symbol);

                                            // Dashboard disabled for performance
                                            // self.update_token_dashboard_with_bonding_curve(...).await;

                                            // Execute dip buy with coordination
                                            {
                                                let mut coordinator = self.trade_coordinator.lock().await;
                                                coordinator.start_trading(token_data.symbol.clone());
                                            }
                                            self.enhanced_state_machine.start_trading(token_data.symbol.clone());

                                            if let Err(e) = self.execute_dip_buy_order(&token_data).await {
                                                eprintln!("❌ Failed to execute dip buy: {}", e);
                                                // Update dashboard status to failed
                                                // self.update_token_dashboard_status(&token_data.mint, "failed").await;
                                                self.enhanced_state_machine.end_trading();
                                                {
                                                    let mut coordinator = self.trade_coordinator.lock().await;
                                                    coordinator.end_trading(false); // Don't purge on failure
                                                }
                                            }
                                        }
                                    }
                                    Err(e) => {
                                        eprintln!("⚠️  Dip monitoring failed: {}", e);
                                    }
                                }
                            }
                        }

                        // Check for positions that should exit (including bonding curve graduation)
                        let mut positions_to_exit = self.position_manager.get_positions_to_exit().await;

                        // Add bonding curve graduation checks for all active positions
                        let active_positions = self.position_manager.get_all_positions().await;
                        for (token_mint, position) in active_positions {
                            // Check actual bonding curve progress for graduation risk
                            if let Ok(graduation_check) = self.check_bonding_curve_graduation(&token_mint).await {
                                if graduation_check.should_sell {
                                    println!("🚨 BONDING CURVE GRADUATION RISK: {} at {:.1}% progress",
                                            position.token_symbol, graduation_check.progress * 100.0);

                                    positions_to_exit.push((token_mint.clone(), ExitReason::TokenGraduation {
                                        graduation_price: graduation_check.current_price,
                                        bonding_curve_progress: graduation_check.progress,
                                    }));
                                }
                            }
                        }

                        if !positions_to_exit.is_empty() {
                            println!("🎯 PROCESSING {} SELL TRIGGERS", positions_to_exit.len());

                            for (token_mint, exit_reason) in positions_to_exit {
                                println!("🚀 SELL TRIGGER: {} - {}", &token_mint[..8], exit_reason.description());

                                // Execute sell order directly (atomic protection handled by position manager)
                                if let Err(e) = self.execute_sell_for_token(&token_mint, exit_reason).await {
                                    eprintln!("❌ Failed to execute sell order for {}: {}", token_mint, e);
                                } else {
                                    println!("✅ SELL EXECUTED: {}", token_mint);
                                }
                            }
                        }

                        self.check_kill_switches().await;

                        // Show heartbeat and position summary
                        let active_count = self.position_manager.active_count().await;
                        if active_count > 0 {
                            println!("💓 Main loop heartbeat - {} active positions", active_count);
                            self.position_manager.print_summary().await;
                        } else {
                            // Show enhanced state machine status when no active positions
                            let state_summary = self.enhanced_state_machine.get_status_summary();
                            if !state_summary.contains("Scanning") {
                                println!("💓 {}", state_summary);
                            }
                        }

                        // PERIODIC SCANNING FOR ESTABLISHED TOKENS (DIP-BUY STRATEGY)
                        // Only scan when no active positions and enough time has passed
                        if active_count == 0 && last_periodic_scan.elapsed() >= periodic_scan_interval {
                            println!("🔄 PERIODIC SCAN: Looking for established tokens (600+ seconds old)");
                            println!("   💡 Dip-buy strategy: Focus on existing tokens with trading history");

                            // Try ultra-conservative IDL scanning for established tokens
                            match self.token_scanner.scan_existing_tokens_via_idl_with_batch_size(1).await {
                                Ok(qualifying_tokens) => {
                                    if qualifying_tokens > 0 {
                                        println!("✅ Periodic scan found {} qualifying established tokens", qualifying_tokens);
                                        println!("   🎯 These tokens are now available for dip-buy monitoring");
                                    } else {
                                        println!("📊 Periodic scan completed - no new qualifying tokens found");
                                    }
                                }
                                Err(e) => {
                                    println!("⚠️  Periodic scan failed: {}", e);
                                    println!("   💡 Will retry in next cycle ({}s)", periodic_scan_interval.as_secs());
                                }
                            }

                            last_periodic_scan = std::time::Instant::now();
                            println!("   ⏰ Next periodic scan in {} minutes (optimized 2-min interval)", periodic_scan_interval.as_secs() / 60);
                        }
                    }
                }
            }
        }

        println!("🏁 Live trading session ended");
        Ok(())
    }

    // WebSocket monitoring moved to websocket_monitor module

    /// Parse pump.fun token creation event from WebSocket API
    fn parse_pumpfun_token_creation(json_value: &serde_json::Value) -> Result<TokenCreationEvent> {
        let name = json_value.get("name")
            .and_then(|v| v.as_str())
            .unwrap_or("Unknown")
            .to_string();

        let symbol = json_value.get("symbol")
            .and_then(|v| v.as_str())
            .unwrap_or("UNKNOWN")
            .to_string();

        let mint = json_value.get("mint")
            .and_then(|v| v.as_str())
            .unwrap_or("")
            .to_string();

        let uri = json_value.get("uri")
            .or_else(|| json_value.get("metadataUri"))
            .and_then(|v| v.as_str())
            .unwrap_or("")
            .to_string();

        let creator = json_value.get("traderPublicKey")
            .and_then(|v| v.as_str())
            .unwrap_or("")
            .to_string();

        let bonding_curve = json_value.get("bondingCurveKey")
            .and_then(|v| v.as_str())
            .unwrap_or("")
            .to_string();

        let market_cap_sol = json_value.get("marketCapSol")
            .and_then(|v| v.as_f64())
            .unwrap_or(0.0);

        let liquidity_sol = json_value.get("vSolInBondingCurve")
            .and_then(|v| v.as_f64())
            .unwrap_or(0.0);

        let initial_buy_sol = json_value.get("solAmount")
            .and_then(|v| v.as_f64())
            .unwrap_or(0.0);

        let initial_buy_tokens = json_value.get("initialBuy")
            .and_then(|v| v.as_f64())
            .unwrap_or(0.0);

        let signature = json_value.get("signature")
            .and_then(|v| v.as_str())
            .unwrap_or("")
            .to_string();

        let tx_type = json_value.get("txType")
            .and_then(|v| v.as_str())
            .unwrap_or("create")
            .to_string();

        let timestamp = json_value.get("timestamp")
            .and_then(|v| v.as_u64())
            .unwrap_or(0);

        Ok(TokenCreationEvent {
            mint,
            name,
            symbol,
            uri,
            creator,
            bonding_curve,
            market_cap_sol,
            liquidity_sol,
            initial_buy_sol,
            initial_buy_tokens,
            signature,
            tx_type,
            timestamp,
            initial_liquidity: liquidity_sol,
        })
    }

    /// Parse pump.fun trade event from WebSocket API
    fn parse_pumpfun_trade_event(json_value: &serde_json::Value) -> Result<TradeEvent> {
        let mint = json_value.get("mint")
            .and_then(|v| v.as_str())
            .unwrap_or("")
            .to_string();

        let trader = json_value.get("traderPublicKey")
            .and_then(|v| v.as_str())
            .unwrap_or("")
            .to_string();

        let side = json_value.get("txType")
            .and_then(|v| v.as_str())
            .unwrap_or("unknown")
            .to_string();

        let amount_sol = json_value.get("solAmount")
            .and_then(|v| v.as_f64())
            .unwrap_or(0.0);

        let amount_tokens = json_value.get("tokenAmount")
            .and_then(|v| v.as_f64())
            .unwrap_or(0.0) as u64;

        let price = if amount_tokens > 0 {
            amount_sol / (amount_tokens as f64)
        } else {
            0.0
        };

        let timestamp = json_value.get("timestamp")
            .and_then(|v| v.as_u64())
            .unwrap_or(0);

        let signature = json_value.get("signature")
            .and_then(|v| v.as_str())
            .unwrap_or("")
            .to_string();

        Ok(TradeEvent {
            mint,
            trader,
            side,
            amount_sol,
            amount_tokens,
            price,
            timestamp,
            signature,
        })
    }

    /// Handle new token creation event
    async fn handle_new_token(&mut self, token_event: TokenCreationEvent) -> Result<()> {
        println!("🔍 Evaluating new token: {} ({})", token_event.name, token_event.symbol);

        // Add token to dashboard tracking immediately upon discovery
        self.add_token_to_dashboard(&token_event, "discovered").await;

        // Convert TokenCreationEvent to LiveTokenData for filtering
        let token_data = self.convert_to_live_token_data(&token_event);

        // Apply trading filters
        if !self.should_trade_token(&token_data) {
            // Dashboard disabled for performance
            // self.update_token_dashboard_status(&token_event.mint, "filtered").await;
            return Ok(());
        }

        println!("✅ Token passed filters - executing trade!");

        // Dashboard disabled for performance
        // self.update_token_dashboard_status(&token_event.mint, "candidate").await;

        // Add configurable delay to allow bonding curve account initialization
        println!("⏳ Waiting {} seconds for bonding curve initialization...", self.trading_config.bonding_curve_wait_seconds);
        tokio::time::sleep(tokio::time::Duration::from_secs(self.trading_config.bonding_curve_wait_seconds)).await;

        // Execute buy order and add to position manager
        match self.execute_buy_order(&token_data).await {
            Ok((amount_tokens, amount_sol, entry_price)) => {
                // Add position to state machine
                if let Err(e) = self.position_manager.add_position(
                    token_data.mint.clone(),
                    token_data.symbol.clone(),
                    amount_tokens,
                    amount_sol,
                    entry_price,
                ).await {
                    eprintln!("❌ Failed to add position to manager: {}", e);
                } else {
                    self.total_trades += 1;
                    println!("🎯 Position opened successfully and added to state machine!");
                }
            }
            Err(e) => {
                eprintln!("❌ Failed to execute buy order: {}", e);
            }
        }

        Ok(())
    }

    /// Handle new token creation event with dip-buy support
    async fn handle_new_token_with_dip_support(&mut self, token_event: TokenCreationEvent) -> Result<()> {
        println!("🔍 Evaluating new token: {} ({})", token_event.name, token_event.symbol);

        // Add token to dashboard tracking immediately upon discovery
        self.add_token_to_dashboard(&token_event, "discovered").await;

        // Convert TokenCreationEvent to LiveTokenData for filtering
        let token_data = self.convert_to_live_token_data(&token_event);

        // Apply trading filters
        if !self.should_trade_token(&token_data) {
            // Dashboard disabled for performance
            // self.update_token_dashboard_status(&token_event.mint, "filtered").await;
            return Ok(());
        }

        println!("✅ Token passed filters!");

        // Dashboard disabled for performance
        // self.update_token_dashboard_status(&token_event.mint, "candidate").await;

        // Check if dip-buy is enabled
        if DipMonitor::is_enabled() {
            // Try to add token to watch list
            match self.enhanced_state_machine.handle_new_token(token_data).await {
                Ok(should_buy_immediately) => {
                    if should_buy_immediately {
                        // Immediate buy (dip-buy disabled or other conditions)
                        println!("🚀 Executing immediate buy (dip-buy bypassed)");
                        self.execute_immediate_buy_order(&token_event).await?;
                    } else {
                        // Token added to watch list
                        println!("👁️  Token added to dip-buy watch list");
                    }
                }
                Err(e) => {
                    println!("⚠️  Failed to add token to watch list: {}", e);
                    // Fallback to immediate buy
                    println!("🚀 Falling back to immediate buy");
                    self.execute_immediate_buy_order(&token_event).await?;
                }
            }
        } else {
            // Dip-buy disabled, execute immediate buy
            println!("🚀 Executing immediate buy (dip-buy disabled)");
            self.execute_immediate_buy_order(&token_event).await?;
        }

        Ok(())
    }

    /// Execute immediate buy order (original behavior)
    async fn execute_immediate_buy_order(&mut self, token_event: &TokenCreationEvent) -> Result<()> {
        let token_data = self.convert_to_live_token_data(token_event);

        // Add configurable delay to allow bonding curve account initialization
        println!("⏳ Waiting {} seconds for bonding curve initialization...", self.trading_config.bonding_curve_wait_seconds);
        tokio::time::sleep(tokio::time::Duration::from_secs(self.trading_config.bonding_curve_wait_seconds)).await;

        // Execute buy order and add to position manager
        match self.execute_buy_order(&token_data).await {
            Ok((amount_tokens, amount_sol, entry_price)) => {
                // Dashboard disabled for performance
                // self.update_token_dashboard_status(&token_event.mint, "trading").await;

                // Add position to state machine
                if let Err(e) = self.position_manager.add_position(
                    token_data.mint.clone(),
                    token_data.symbol.clone(),
                    amount_tokens,
                    amount_sol,
                    entry_price,
                ).await {
                    eprintln!("❌ Failed to add position to manager: {}", e);
                    // Dashboard disabled for performance
                    // self.update_token_dashboard_status(&token_event.mint, "failed").await;
                } else {
                    self.total_trades += 1;
                    println!("🎯 Position opened successfully and added to state machine!");
                }
            }
            Err(e) => {
                eprintln!("❌ Failed to execute buy order: {}", e);
                // Dashboard disabled for performance
                // self.update_token_dashboard_status(&token_event.mint, "failed").await;
            }
        }

        Ok(())
    }

    /// Execute buy order for graduation candidate from aging pipeline
    pub async fn execute_graduation_candidate_buy(&mut self, token_data: &LiveTokenData) -> Result<()> {
        println!("🎓 EXECUTING GRADUATION CANDIDATE BUY: {}", token_data.name);
        println!("   📊 Liquidity: {:.1} SOL | Market Cap: ${:.0} | Holders: {}",
            token_data.liquidity_sol,
            token_data.market_cap_usd,
            token_data.holders);

        // Get current price for dip-buy analysis
        let current_price = self.get_current_token_price(&token_data.mint).await.unwrap_or(0.0);
        println!("   💰 EXECUTION PRICE: {:.9} SOL/token (current market price)", current_price);

        // This internal function handles the buy, and upon success, adds the position to the manager.
        self.execute_buy_order_internal(token_data, "GRADUATION_CANDIDATE").await
    }

    /// Execute buy order for a dip opportunity
    async fn execute_dip_buy_order(&mut self, token_data: &LiveTokenData) -> Result<()> {
        println!("🎯 Executing dip-buy order for {} ({})", token_data.name, token_data.symbol);

        // Get current price for dip-buy analysis
        let current_price = self.get_current_token_price(&token_data.mint).await.unwrap_or(0.0);
        println!("   💰 DIP-BUY EXECUTION PRICE: {:.9} SOL/token", current_price);

        self.execute_buy_order_internal(token_data, "DIP_BUY").await
    }

    /// Get current token price from bonding curve
    async fn get_current_token_price(&self, mint: &str) -> Result<f64> {
        let mint_pubkey = Pubkey::from_str(mint)?;
        let (bonding_curve, _) = self.pump_instruction_builder.derive_bonding_curve(&mint_pubkey);

        match self.rpc_client.get_account(&bonding_curve) {
            Ok(account) => {
                let curve_data = crate::live_pump_integration::BondingCurveState::from_account_data(&account.data)?;

                // Calculate current price (SOL per token)
                let current_price = if curve_data.virtual_token_reserves > 0 {
                    curve_data.virtual_sol_reserves as f64 / curve_data.virtual_token_reserves as f64
                } else {
                    0.0
                };

                Ok(current_price)
            }
            Err(e) => Err(anyhow!("Failed to get bonding curve account: {}", e))
        }
    }

    /// Internal buy order execution (shared by dip-buy and graduation candidate)
    async fn execute_buy_order_internal(&mut self, token_data: &LiveTokenData, order_type: &str) -> Result<()> {
        println!("💰 {} ORDER: {} ({})", order_type, token_data.name, token_data.symbol);

        // Execute buy order and add to position manager
        match self.execute_buy_order(token_data).await {
            Ok((amount_tokens, amount_sol, entry_price)) => {
                // Temporarily disabled dashboard update to prevent panics
                // self.update_token_dashboard_status(&token_data.mint, "trading").await;

                // Add position to state machine
                if let Err(e) = self.position_manager.add_position(
                    token_data.mint.clone(),
                    token_data.symbol.clone(),
                    amount_tokens,
                    amount_sol,
                    entry_price,
                ).await {
                    eprintln!("❌ Failed to add position to manager: {}", e);
                    // Temporarily disabled dashboard update
                    // self.update_token_dashboard_status(&token_data.mint, "failed").await;
                    self.enhanced_state_machine.end_trading();
                } else {
                    self.total_trades += 1;
                    println!("🎯 {} position opened successfully!", order_type);
                }
            }
            Err(e) => {
                eprintln!("❌ Failed to execute {} order: {}", order_type, e);
                // Temporarily disabled dashboard update
                // self.update_token_dashboard_status(&token_data.mint, "failed").await;
                self.enhanced_state_machine.end_trading();
                return Err(e);
            }
        }

        Ok(())
    }

    /// Add token to dashboard tracking
    async fn add_token_to_dashboard(&self, token_event: &TokenCreationEvent, status: &str) {
        // Calculate basic metrics for dashboard
        let progress = 0.0; // New tokens start at 0% progress
        let sol_reserves = token_event.liquidity_sol;
        let market_cap = token_event.market_cap_sol * self.trading_config.sol_to_usd_rate;

        // Dashboard disabled for performance optimization
        // self.web_dashboard.update_token(...);
        println!("📊 Token {} processed with status: {} (dashboard disabled)", token_event.symbol, status);
    }

    /// Dashboard methods disabled for performance optimization
    // async fn update_token_dashboard_status(&self, mint: &str, status: &str) { ... }
    // async fn update_token_dashboard_with_bonding_curve(&self, ...) { ... }
    // async fn remove_token_from_dashboard(&self, mint: &str, symbol: &str) { ... }

    /// Convert TokenCreationEvent to LiveTokenData for filtering
    fn convert_to_live_token_data(&self, event: &TokenCreationEvent) -> LiveTokenData {
        // Use the actual token creation timestamp from WebSocket event, fallback to current time if not available
        let creation_time = if event.timestamp > 0 {
            event.timestamp
        } else {
            SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs()
        };

        // Calculate market cap in USD using configurable SOL to USD rate
        let market_cap_usd = event.market_cap_sol * self.trading_config.sol_to_usd_rate;

        // Calculate price per token
        let price_sol = if event.initial_buy_tokens > 0.0 {
            event.initial_buy_sol / event.initial_buy_tokens
        } else {
            0.000000001 // Very small default price
        };

        LiveTokenData {
            mint: event.mint.clone(),
            name: event.name.clone(),
            symbol: event.symbol.clone(),
            description: "New pump.fun token".to_string(),
            image: event.uri.clone(),
            creator: event.creator.clone(),
            creation_time, // Use actual token creation timestamp
            market_cap_usd,
            liquidity_sol: event.liquidity_sol,
            price_sol,
            volume_24h: event.initial_buy_sol, // Use initial buy as volume
            holders: 1, // New token starts with 1 holder (creator)
            transactions: 1, // Creation transaction
            is_complete: false,
            bonding_curve: event.bonding_curve.clone(),
            associated_bonding_curve: event.bonding_curve.clone(), // Use same for now
        }
    }

    /// Fetch detailed token data from pump.fun API
    async fn fetch_token_data(&self, mint: &str) -> Result<LiveTokenData> {
        let url = format!("{}/coins/{}", PUMP_API_BASE, mint);
        let response = self.http_client.get(&url).send().await?;

        if !response.status().is_success() {
            return Err(anyhow!("Failed to fetch token data: {}", response.status()));
        }

        let token_data: LiveTokenData = response.json().await?;
        Ok(token_data)
    }

    /// Note: Price updates now come from WebSocket trade events via update_position_prices()
    /// This prevents 503 Service Unavailable errors from excessive HTTP API calls

    /// Apply trading filters to determine if we should trade this token
    fn should_trade_token(&self, token: &LiveTokenData) -> bool {
        // Use the extracted token filters module
        crate::live_pump_integration::TokenFilters::should_trade_token(token, &self.trading_config)
    }

    /// Execute buy order for a token using extracted trade executor
    /// Returns (amount_tokens, amount_sol, entry_price) for position manager
    async fn execute_buy_order(&mut self, token: &LiveTokenData) -> Result<(u64, f64, f64)> {
        // Use the extracted trade executor module
        match self.trade_executor.execute_buy_order(token).await {
            Ok(buy_result) => {
                Ok((buy_result.amount_tokens, buy_result.amount_sol, buy_result.entry_price))
            }
            Err(e) => Err(e)
        }
    }

    /// Legacy execute buy order implementation (moved to trade_executor module)
    async fn _legacy_execute_buy_order(&mut self, token: &LiveTokenData) -> Result<(u64, f64, f64)> {
        println!("🚀 Executing buy order for {} ({})", token.name, token.symbol);

        // Calculate position size using environment variable percentage
        let wallet_balance = self.get_wallet_balance().await?;
        let position_size_percent = self.trading_config.base_position_percent / 100.0;
        let mut position_size = wallet_balance * position_size_percent;

        // Apply min/max trade amount limits from environment variables
        position_size = position_size.max(self.trading_config.min_trade_amount);
        position_size = position_size.min(self.trading_config.max_trade_amount);

        println!("   💰 Position size: {:.6} SOL", position_size);
        println!("   📊 Current price: {:.9} SOL", token.price_sol);

        // Using priority fees only (Jito disabled)
        println!("⚡ Executing with Priority Fees...");
        self.execute_buy_with_priority_fees(token, position_size).await
    }

    /// Execute buy order using priority fees
    /// Returns (amount_tokens, amount_sol, entry_price) for position manager
    async fn execute_buy_with_priority_fees(&mut self, token: &LiveTokenData, position_size: f64) -> Result<(u64, f64, f64)> {
        let mint = Pubkey::from_str(&token.mint)?;

        // Get bonding curve data to extract creator and reserves
        let (bonding_curve, _) = self.pump_instruction_builder.derive_bonding_curve(&mint);

        // Fetch bonding curve account to get creator and reserves
        let bonding_curve_account = match self.rpc_client.get_account(&bonding_curve) {
            Ok(account) => {
                println!("✅ Bonding curve account found, parsing data...");
                BondingCurveAccount::from_account_data(&account.data)?
            }
            Err(e) => {
                return Err(anyhow!("Failed to fetch bonding curve account: {}", e));
            }
        };

        let virtual_sol_reserves = bonding_curve_account.virtual_sol_reserves;
        let virtual_token_reserves = bonding_curve_account.virtual_token_reserves;
        let creator = bonding_curve_account.creator;

        println!("📊 Bonding Curve Data:");
        println!("   Virtual SOL Reserves: {} lamports ({:.6} SOL)",
            virtual_sol_reserves, virtual_sol_reserves as f64 / 1_000_000_000.0);
        println!("   Virtual Token Reserves: {}", virtual_token_reserves);
        println!("   Creator: {}", creator);
        println!("   Active: {}", bonding_curve_account.is_active());

        if !bonding_curve_account.is_active() {
            return Err(anyhow!("Bonding curve is complete (migrated to Raydium)"));
        }

        // Use configurable amount for testing
        let test_sol_amount = self.trading_config.test_sol_amount;
        let sol_cost_lamports = (test_sol_amount * 1_000_000_000.0) as u64;

        // Calculate expected tokens and max cost
        let expected_tokens = self.pump_instruction_builder.calculate_tokens_out(
            test_sol_amount,
            virtual_sol_reserves,
            virtual_token_reserves
        );
        let max_sol_cost = self.pump_instruction_builder.calculate_max_sol_cost(sol_cost_lamports, 15.0); // 15% slippage for better execution

        println!("   🧮 Expected tokens: {}", expected_tokens);
        println!("   💰 Max SOL cost: {} lamports", max_sol_cost);

        // Always create ATA instruction (idempotent - won't fail if exists)
        let ata_instruction = Some(self.pump_instruction_builder.create_ata_instruction(
            &self.wallet_keypair.pubkey(),
            &self.wallet_keypair.pubkey(),
            &mint,
        ));

        // Use expected tokens directly (pump.fun expects base units, not decimal-adjusted)
        let token_amount = expected_tokens; // Use raw token amount from bonding curve calculation

        // Build buy instruction with creator for creator vault derivation
        let buy_instruction = self.pump_instruction_builder.build_buy_instruction(
            &self.wallet_keypair.pubkey(),
            &mint,
            token_amount,     // Token amount to buy (base units)
            max_sol_cost,     // Maximum SOL cost for slippage protection
            &creator,         // Pass creator for creator vault derivation
        )?;

        // Execute with priority fees
        if let Some(ref priority_executor) = self.priority_executor {
            match priority_executor.execute_buy(buy_instruction, ata_instruction, &self.wallet_keypair).await {
                Ok(tx_result) if tx_result.success => {
                    println!("✅ Buy order executed successfully with Priority Fees!");
                    println!("   🪙 Tokens received: {}", expected_tokens);
                    println!("   📡 Transaction: {}", tx_result.signature);
                    println!("   ⚡ Confirmation time: {}ms", tx_result.confirmation_time_ms);
                    println!("   💰 Priority fee paid: {} lamports", tx_result.total_cost_lamports);

                    // Return data for position manager
                    Ok((expected_tokens, position_size, token.price_sol))
                }
                Ok(_) => {
                    Err(anyhow!("Priority fees transaction failed"))
                }
                Err(e) => {
                    Err(anyhow!("Priority fees execution failed: {}", e))
                }
            }
        } else {
            Err(anyhow!("Priority fees executor not initialized"))
        }
    }

    /// Execute buy order using Jito (fallback method) - DEPRECATED
    /// Returns (amount_tokens, amount_sol, entry_price) for position manager
    async fn execute_buy_with_jito(&mut self, token: &LiveTokenData, position_size: f64) -> Result<(u64, f64, f64)> {
        // Create simplified buy instruction for Jito
        let buy_instruction = self.create_pump_buy_instruction(token, position_size).await?;

        match self.jito_client.execute_pump_trade(
            buy_instruction,
            &self.wallet_keypair,
            TradingPriority::High,
        ).await {
            Ok(trade_result) if trade_result.success => {
                let tokens_received = (position_size / token.price_sol) as u64;

                println!("✅ Buy order executed successfully with Jito!");
                println!("   🪙 Tokens received: {}", tokens_received);
                println!("   🎯 Bundle ID: {}", trade_result.bundle_id);
                println!("   💰 Tip paid: {} lamports", trade_result.tip_paid);

                // Return data for position manager
                Ok((tokens_received, position_size, token.price_sol))
            }
            _ => {
                Err(anyhow!("Jito buy execution failed"))
            }
        }
    }

    /// Execute sell order using priority fees - DEPRECATED (replaced by state machine)
    async fn execute_sell_with_priority_fees_deprecated(&mut self, _position: &str) -> Result<TransactionResult> {
        // DEPRECATED: This function is replaced by state machine sell execution
        println!("⚠️ DEPRECATED: Old sell function called - use state machine instead");
        Err(anyhow!("Function deprecated - use state machine sell execution"))
    }

    /// Execute sell order using Jito (fallback method) - DEPRECATED
    async fn execute_sell_with_jito_deprecated(&mut self, _token_mint: &str) -> Result<TransactionResult> {
        // DEPRECATED: This function is replaced by state machine sell execution
        println!("⚠️ DEPRECATED: Old Jito sell function called - use state machine instead");
        Err(anyhow!("Function deprecated - use state machine sell execution"))
    }

    /// Create pump.fun buy instruction
    async fn create_pump_buy_instruction(&self, token: &LiveTokenData, amount_sol: f64) -> Result<Instruction> {
        let _mint_pubkey = Pubkey::from_str(&token.mint)?;
        let _bonding_curve = Pubkey::from_str(&token.bonding_curve)?;
        let _associated_bonding_curve = Pubkey::from_str(&token.associated_bonding_curve)?;

        // This is a simplified instruction - in production you'd need the full pump.fun instruction format
        Ok(system_instruction::transfer(
            &self.wallet_keypair.pubkey(),
            &self.pump_fee_recipient,
            (amount_sol * 1_000_000_000.0) as u64, // Convert to lamports
        ))
    }

    /// Create pump.fun sell instruction - DEPRECATED
    async fn create_pump_sell_instruction_deprecated(&self, _token_mint: &str) -> Result<Instruction> {
        // DEPRECATED: This function is replaced by state machine sell execution
        println!("⚠️ DEPRECATED: Old sell instruction function called - use state machine instead");
        Err(anyhow!("Function deprecated - use state machine sell execution"))
    }

    /// DEPRECATED: Position price updates now handled by state machine
    async fn update_position_prices_deprecated(&mut self, _trade_event: &TradeEvent) {
        // DEPRECATED: Price updates now handled by position manager state machine
        println!("⚠️ DEPRECATED: Old price update function called - using state machine instead");
    }

    /// DEPRECATED: Position exit checking now handled by state machine
    async fn check_position_exits_deprecated(&mut self) -> Result<()> {
        // DEPRECATED: Exit checking now handled by position manager state machine
        println!("⚠️ DEPRECATED: Old exit checking function called - using state machine instead");
        Ok(())
    }

    /// DEPRECATED: Exit logic now handled by state machine
    fn should_exit_position_deprecated(&self, _token_mint: &str) -> (bool, String) {
        // DEPRECATED: Exit logic now handled by position manager state machine
        println!("⚠️ DEPRECATED: Old exit logic function called - using state machine instead");
        (false, "Deprecated function".to_string())
    }

    // Removed simulation code - this is production trading bot

    /// Check kill switches and terminate session if triggered
    async fn check_kill_switches(&mut self) {
        if !self.enable_kill_switches || !self.session_active {
            return;
        }

        // Check P&L kill switch using configurable starting capital
        let total_pnl_percent = (self.total_pnl / self.trading_config.starting_capital_sol) * 100.0;
        if total_pnl_percent >= self.max_pnl_percent {
            println!("🎯 PROFIT TARGET REACHED!");
            println!("   💰 Total P&L: {:.6} SOL ({:+.1}%)", self.total_pnl, total_pnl_percent);
            println!("   🏆 Target: +{:.0}% - MISSION ACCOMPLISHED!", self.max_pnl_percent);
            self.session_active = false;
            return;
        }

        // Check consecutive losses kill switch
        if self.consecutive_losses >= self.max_consecutive_losses {
            println!("🛡️  CONSECUTIVE LOSS LIMIT REACHED!");
            println!("   📉 Consecutive losses: {}/{}", self.consecutive_losses, self.max_consecutive_losses);
            println!("   🛡️  Protecting capital - session terminated");
            self.session_active = false;
            return;
        }

        // Display current status periodically
        if self.total_trades > 0 && self.total_trades % 5 == 0 {
            let win_rate = (self.successful_trades as f64 / self.total_trades as f64) * 100.0;
            println!("📊 Session Status: P&L {:.6} SOL ({:+.1}%) | Win Rate {:.1}% | Consecutive Losses: {}/{}",
                    self.total_pnl, total_pnl_percent, win_rate, self.consecutive_losses, self.max_consecutive_losses);

            // Print dashboard summary every 5 trades
            println!("\n📈 PERFORMANCE DASHBOARD UPDATE");
            self.dashboard.print_dashboard();
        }
    }

    /// DEPRECATED: Sell execution now handled by state machine
    async fn execute_sell_order_deprecated(&mut self, _token_mint: &str) -> Result<()> {
        // DEPRECATED: Sell execution now handled by position manager state machine
        println!("⚠️ DEPRECATED: Old sell execution function called - using state machine instead");
        Err(anyhow!("Function deprecated - use state machine sell execution"))
    }

    /// Get current wallet balance
    async fn get_wallet_balance(&self) -> Result<f64> {
        let balance = self.rpc_client.get_balance(&self.wallet_keypair.pubkey())?;
        Ok(balance as f64 / 1_000_000_000.0) // Convert lamports to SOL
    }

    // Removed test position creation - this is production trading bot

    /// DEPRECATED: Exit reason determination now handled by state machine
    fn determine_exit_reason_deprecated(&self, _token_mint: &str) -> String {
        // DEPRECATED: Exit reason determination now handled by position manager state machine
        "State Machine Exit".to_string()
    }

    /// Execute sell order for a specific token using state machine
    async fn execute_sell_for_token(&mut self, token_mint: &str, exit_reason: ExitReason) -> Result<()> {
        println!("🚀 EXECUTING SELL ORDER: {} - {}", token_mint, exit_reason.description());

        // Get position details from position manager
        let position = match self.position_manager.get_position(token_mint).await {
            Some(pos) => pos,
            None => {
                println!("❌ Position not found for token: {}", token_mint);
                return Err(anyhow!("Position not found"));
            }
        };

        // Extract entry price and time from state
        let (entry_price, entry_time) = match &position.state {
            PositionState::MONITORING { entry_price, entry_time, .. } |
            PositionState::TRAILING_ACTIVE { entry_price, entry_time, .. } => (*entry_price, *entry_time),
            _ => {
                println!("❌ Position in invalid state for selling: {:?}", position.state);
                return Err(anyhow!("Position in invalid state"));
            }
        };

        println!("📤 Executing sell order for {} ({})", position.token_symbol, position.token_symbol);
        println!("   💰 Amount: {} tokens", position.amount_tokens);
        println!("   📊 Entry price: {:.9} SOL", entry_price);

        // Execute the actual sell transaction
        match self.execute_pump_sell_transaction(&position).await {
            Ok(tx_result) => {
                if tx_result.success {
                    let hold_duration = entry_time.elapsed();
                    // Calculate actual P&L from sell transaction
                    let realized_pnl = if let Some(position) = self.position_manager.get_position(token_mint).await {
                        let sell_amount_sol = tx_result.total_cost_lamports as f64 / 1_000_000_000.0;
                        sell_amount_sol - position.amount_sol
                    } else {
                        0.0
                    };

                    println!("✅ Sell order executed successfully!");
                    println!("   📡 Transaction: {}", tx_result.signature);
                    println!("   💰 Fee paid: {} lamports", tx_result.total_cost_lamports);
                    println!("   ⏰ Hold time: {:.1} minutes", hold_duration.as_secs_f64() / 60.0);
                    println!("   🎯 Exit reason: {}", exit_reason.description());

                    // Update dashboard status to sold
                    let status = match exit_reason {
                        ExitReason::TakeProfit { .. } => "sold_profit",
                        ExitReason::StopLoss { .. } => "sold_loss",
                        ExitReason::TrailingStop { .. } => "sold_trailing",
                        ExitReason::MaxHoldTime { .. } => "sold_timeout",
                        ExitReason::TokenGraduation { .. } => "sold_graduation",
                        ExitReason::ManualSell { .. } => "sold_manual",
                        ExitReason::EmergencySell { .. } => "sold_emergency",
                    };
                    // Dashboard disabled for performance
                    // self.update_token_dashboard_status(token_mint, status).await;

                    // Remove position from state machine
                    self.position_manager.remove_position(token_mint).await;

                    // Update trading statistics
                    self.total_trades += 1;
                    self.total_pnl += realized_pnl;

                    if realized_pnl > 0.0 {
                        self.successful_trades += 1;
                        self.consecutive_losses = 0;
                        println!("🎉 PROFIT: +{:.6} SOL", realized_pnl);
                    } else {
                        self.consecutive_losses += 1;
                        println!("📉 LOSS: {:.6} SOL", realized_pnl);
                        println!("⚠️  Consecutive losses: {}/{}", self.consecutive_losses, self.max_consecutive_losses);
                    }

                    println!("📊 Total P&L: {:.6} SOL | Win Rate: {:.1}%",
                            self.total_pnl,
                            (self.successful_trades as f64 / self.total_trades.max(1) as f64) * 100.0);

                    println!("✅ SELL TRIGGER COMPLETED: {} - {}", token_mint, exit_reason.code());
                    Ok(())
                } else {
                    println!("❌ Sell transaction failed: {:?}", tx_result.signature);
                    Err(anyhow!("Sell transaction failed"))
                }
            }
            Err(e) => {
                println!("❌ Failed to execute sell transaction: {}", e);
                Err(e)
            }
        }
    }

    /// Execute pump.fun sell transaction for a position
    async fn execute_pump_sell_transaction(&mut self, position: &ManagedPosition) -> Result<TransactionResult> {
        let mint = Pubkey::from_str(&position.token_mint)?;

        // Get bonding curve state for calculations
        let (bonding_curve, _) = self.pump_instruction_builder.derive_bonding_curve(&mint);
        let (virtual_sol_reserves, virtual_token_reserves, creator) = get_bonding_curve_state(&self.rpc_client, &bonding_curve).await?;

        // Calculate expected SOL output and minimum output
        let expected_sol_output = self.pump_instruction_builder.calculate_sol_out(
            position.amount_tokens,
            virtual_sol_reserves,
            virtual_token_reserves
        );
        let min_sol_output = self.pump_instruction_builder.calculate_min_sol_output(expected_sol_output, 20.0); // 20% slippage

        println!("   🧮 Expected SOL output: {} lamports", expected_sol_output);
        println!("   💰 Min SOL output: {} lamports", min_sol_output);

        // Build sell instruction with creator for creator vault derivation
        let sell_instruction = self.pump_instruction_builder.build_sell_instruction(
            &self.wallet_keypair.pubkey(),
            &mint,
            position.amount_tokens,
            min_sol_output,
            &creator,  // Pass creator for creator vault derivation
        )?;

        // Execute with priority fees
        if let Some(ref priority_executor) = self.priority_executor {
            match priority_executor.execute_sell(sell_instruction, &self.wallet_keypair).await {
                Ok(tx_result) if tx_result.success => {
                    println!("✅ Sell order executed successfully with Priority Fees!");
                    println!("   💰 SOL received: {} lamports", expected_sol_output);
                    println!("   📡 Transaction: {}", tx_result.signature);
                    println!("   ⚡ Confirmation time: {}ms", tx_result.confirmation_time_ms);
                    println!("   💰 Priority fee paid: {} lamports", tx_result.total_cost_lamports);

                    Ok(tx_result)
                }
                Ok(tx_result) => {
                    Err(anyhow!("Priority fees sell transaction failed: {:?}", tx_result.signature))
                }
                Err(e) => {
                    Err(anyhow!("Priority fees sell execution failed: {}", e))
                }
            }
        } else {
            Err(anyhow!("Priority fees executor not initialized"))
        }
    }

    /// Scan existing live tokens using pump.fun API with retry logic
    async fn scan_existing_tokens(&mut self) -> Result<()> {
        println!("🔍 Fetching existing live tokens from pump.fun API...");

        // Gentle retry configuration - prioritize accuracy over speed
        let max_retries = std::env::var("PUMP_SCAN_MAX_ATTEMPTS")
            .unwrap_or_else(|_| "10".to_string())
            .parse::<u32>()
            .unwrap_or(10);
        let mut retry_count = 0;
        let base_delay = tokio::time::Duration::from_secs(10); // Start with 10 second delays

        while retry_count < max_retries {
            // Very gentle token limits - prioritize API stability
            let token_limit = match retry_count {
                0..=2 => 15,  // First few attempts: conservative load
                3..=5 => 10,  // Middle attempts: reduced load
                _ => 5,       // Final attempts: minimal load
            };

            println!("🔍 Gently fetching {} tokens (attempt {}/{})...", token_limit, retry_count + 1, max_retries);

            // Add small delay before each request to be API-friendly
            if retry_count > 0 {
                println!("   ⏳ Waiting before retry to be gentle on API...");
                tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
            }

            // Fetch existing tokens with adaptive limit
            match self.pump_api_client.get_tokens(Some(token_limit), None).await {
                Ok(tokens) => {
                    println!("📊 Found {} existing tokens to evaluate", tokens.len());

                    let mut evaluated = 0;
                    let mut passed_filters = 0;
                    let tokens_len = tokens.len();

                    for token_data in tokens {
                        evaluated += 1;

                        // Check if we should pause due to active positions (Sperm and Egg model)
                        let active_count = self.position_manager.active_count().await;
                        if self.pause_listening_when_trading && active_count > 0 {
                            println!("🔇 PAUSED: Existing token scan stopped - {} active position(s)", active_count);
                            break;
                        }

                        // Convert API data to LiveTokenData format for filtering
                        if let Ok(live_token) = self.convert_api_to_live_token(&token_data) {
                            println!("🔍 Evaluating existing token: {} ({})", live_token.name, live_token.symbol);

                            // Apply same filtering logic as new tokens
                            if self.should_trade_token(&live_token) {
                                passed_filters += 1;
                                println!("✅ Existing token passed filters - executing trade!");

                                // Execute trade using existing logic
                                if let Err(e) = self.execute_buy_order(&live_token).await {
                                    println!("❌ Failed to execute buy for existing token: {}", e);
                                } else {
                                    // Only trade one token at a time (Sperm and Egg model)
                                    break;
                                }
                            }
                        }

                        // Small delay between evaluations to be API-friendly
                        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    }

                    println!("📊 Existing token scan complete: {}/{} tokens evaluated, {} passed filters",
                        evaluated, tokens_len, passed_filters);
                    return Ok(());
                }
                Err(e) => {
                    retry_count += 1;
                    let error_msg = e.to_string();

                    // Gentle error handling - prioritize eventual success over speed
                    if error_msg.contains("503") || error_msg.contains("Service Unavailable") || error_msg.contains("high load") {
                        println!("🚨 API temporarily overloaded (attempt {}/{})", retry_count, max_retries);
                        println!("   📊 High trading activity - being patient for accuracy");

                        if retry_count < max_retries {
                            // Progressive delays: 10s, 20s, 30s, 45s, 60s, etc.
                            let delay_seconds = std::cmp::min(10 + (retry_count * 10) as u64, 120);
                            println!("   ⏳ Waiting {} seconds for API to stabilize...", delay_seconds);
                            tokio::time::sleep(tokio::time::Duration::from_secs(delay_seconds)).await;
                        }
                    } else if error_msg.contains("429") || error_msg.contains("rate limit") {
                        println!("⚠️  Rate limited (attempt {}/{}): Being more gentle", retry_count, max_retries);

                        if retry_count < max_retries {
                            let delay_seconds = 30 + (retry_count * 15) as u64; // 30s, 45s, 60s, etc.
                            println!("   ⏳ Backing off for {} seconds...", delay_seconds);
                            tokio::time::sleep(tokio::time::Duration::from_secs(delay_seconds)).await;
                        }
                    } else {
                        println!("⚠️  API request failed (attempt {}/{}): {}", retry_count, max_retries, e);

                        if retry_count < max_retries {
                            let delay_seconds = 5 + (retry_count * 5) as u64; // 5s, 10s, 15s, etc.
                            println!("   🔄 Retrying in {} seconds...", delay_seconds);
                            tokio::time::sleep(tokio::time::Duration::from_secs(delay_seconds)).await;
                        }
                    }

                    if retry_count >= max_retries {
                        if error_msg.contains("503") || error_msg.contains("Service Unavailable") {
                            println!("📊 API remains overloaded after {} patient attempts", max_retries);
                            println!("   🎯 This indicates extremely high trading activity");
                            println!("   ✅ Proceeding with WebSocket monitoring - we tried our best!");
                        } else {
                            println!("❌ Unable to fetch existing tokens after {} gentle attempts", max_retries);
                            println!("   ✅ Proceeding with WebSocket monitoring for new opportunities");
                        }
                        println!("   💡 Bot will detect new tokens via real-time events");
                        break;
                    }
                }
            }
        }

        Ok(())
    }

    /// Convert PumpApiTokenData to LiveTokenData format
    fn convert_api_to_live_token(&self, api_data: &crate::pump_api_client::PumpApiTokenData) -> Result<LiveTokenData> {
        let current_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();

        // Calculate price from reserves
        let price_sol = if api_data.virtual_token_reserves > 0 {
            api_data.virtual_sol_reserves as f64 / api_data.virtual_token_reserves as f64
        } else {
            0.000000001
        };

        Ok(LiveTokenData {
            mint: api_data.mint.clone(),
            name: api_data.name.clone(),
            symbol: api_data.symbol.clone(),
            description: if api_data.description.is_empty() { "Existing pump.fun token".to_string() } else { api_data.description.clone() },
            image: if api_data.image_uri.is_empty() { "".to_string() } else { api_data.image_uri.clone() },
            creator: api_data.creator.clone(),
            creation_time: current_time - 300, // Assume 5 minutes old for existing tokens
            market_cap_usd: api_data.usd_market_cap,
            liquidity_sol: api_data.virtual_sol_reserves as f64 / 1_000_000_000.0,
            price_sol,
            volume_24h: 0.0, // Not available from API
            holders: 20, // Assume minimum holders for existing tokens
            transactions: 10, // Assume some transaction activity
            is_complete: api_data.complete,
            bonding_curve: "".to_string(), // Will be derived from mint
            associated_bonding_curve: "".to_string(), // Will be derived from mint
        })
    }

    /// Quick API health check to avoid 503 errors
    async fn check_api_health(&self) -> Result<bool> {
        println!("🏥 Performing API health check...");

        // Try a minimal API request with short timeout
        let health_check_url = "https://frontend-api.pump.fun/coins?offset=0&limit=1&sort=created_timestamp&order=DESC";

        let response = tokio::time::timeout(
            tokio::time::Duration::from_secs(5), // Short timeout
            self.http_client.get(health_check_url).send()
        ).await;

        match response {
            Ok(Ok(resp)) => {
                if resp.status().is_success() {
                    println!("✅ API health check passed");
                    Ok(true)
                } else if resp.status() == 503 {
                    println!("🚨 API health check failed: Service overloaded (503)");
                    Ok(false)
                } else {
                    println!("⚠️  API health check failed: Status {}", resp.status());
                    Ok(false)
                }
            }
            Ok(Err(e)) => {
                println!("⚠️  API health check failed: Network error - {}", e);
                Ok(false)
            }
            Err(_) => {
                println!("⚠️  API health check failed: Timeout (>5s)");
                Ok(false)
            }
        }
    }

    // Periodic scanning moved to periodic_scanner module

    // Recent token scanning moved to token_scanner module

    // Token conversion moved to token_scanner module

    // Position monitoring moved to position_monitor module



    /// Check bonding curve graduation risk for a specific token
    async fn check_bonding_curve_graduation(&self, token_mint: &str) -> Result<crate::live_pump_integration::GraduationCheck> {
        // Use the extracted graduation checker module
        crate::live_pump_integration::GraduationChecker::check_bonding_curve_graduation(
            token_mint,
            &self.rpc_client,
            &self.pump_instruction_builder,
        ).await
    }

    /// NEW BUY LOGIC: Scan for graduation candidates using bonding curve analysis
    /// This replaces the risky new token creation sniping strategy
    pub async fn scan_for_graduation_candidates(&mut self) -> Result<bool> {
        println!("🎯 SCANNING FOR GRADUATION CANDIDATES");
        println!("=====================================");
        println!("🔍 Using Chainstack-inspired bonding curve analysis...");
        println!("📊 Looking for tokens with graduation potential...");

        // This function now returns a boolean indicating if a buy was executed.
        match self.bonding_curve_monitor.scan_for_opportunities().await {
            Ok(opportunities) => {
                if opportunities.is_empty() {
                    println!("📭 No graduation candidates found in this scan");
                    return Ok(false);
                }

                println!("✅ Found {} graduation candidates:", opportunities.len());
                for (i, token) in opportunities.iter().enumerate() {
                    println!("   {}. {} ({}) - ${:.0} mcap, {:.1} SOL liquidity",
                        i + 1, token.name, token.symbol, token.market_cap_usd, token.liquidity_sol);
                }

                // The handoff fix processes candidates and returns true if a buy was executed.
                let buy_executed = handoff_fix::process_graduation_candidates(self, opportunities).await;
                Ok(buy_executed)
            }
            Err(e) => {
                println!("❌ Graduation candidate scan failed: {}", e);
                Err(e)
            }
        }
    }

    /// Enhanced trading loop that uses bonding curve monitoring instead of WebSocket sniping
    pub async fn start_enhanced_trading_loop(&mut self) -> Result<()> {
        println!("🚀 STARTING ENHANCED TRADING LOOP");
        println!("=================================");
        println!("🎯 Strategy: Bonding curve graduation analysis");
        println!("📈 Focus: Established tokens with graduation potential");
        println!("⚡ Advantage: Lower risk, higher success probability");
        println!();

        // Start continuous aging pipeline for real-time graduation candidate processing
        if let Err(e) = self.bonding_curve_monitor.start_continuous_aging_pipeline().await {
            println!("⚠️  Failed to start continuous aging pipeline: {}", e);
        }

        // Dashboard disabled for performance optimization
        println!("🚫 Web dashboard DISABLED for performance optimization");
        println!("💡 Focus on core trading functionality - dashboard can be re-enabled later");
        println!();

        let mut scan_counter = 0;
        let scan_interval = Duration::from_secs(120); // 2 minutes between scans

        loop {
            scan_counter += 1;
            println!("\n🔄 SCAN CYCLE #{}", scan_counter);
            println!("⏰ {}", chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"));

            // Check if we should stop trading due to safety limits
            if !self.session_active {
                println!("🛑 Trading session ended due to safety limits");
                break;
            }
            
            // --- POSITION MONITORING AND MANAGEMENT ---
            println!("\n🛡️  MONITORING ACTIVE POSITIONS...");
            let active_positions = self.position_manager.get_all_positions().await;
            if !active_positions.is_empty() {
                println!("   Found {} active positions.", active_positions.len());

                // 1. Update prices and check for trailing stop activation
                for (mint, position) in &active_positions {
                    match self.get_current_token_price(mint).await {
                        Ok(new_price) => {
                            if let Err(e) = self.position_manager.update_position_price(mint, new_price).await {
                                eprintln!("      ❌ Failed to update price for {}: {}", position.token_symbol, e);
                            }
                            if let Some(updated_pos) = self.position_manager.get_position(mint).await {
                                if updated_pos.should_activate_trailing_stop(self.position_manager.config()) {
                                    if let Err(e) = self.position_manager.activate_trailing_stop(mint, new_price).await {
                                        eprintln!("      ❌ Failed to activate trailing stop for {}: {}", updated_pos.token_symbol, e);
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            eprintln!("      ❌ Could not fetch price for {}: {}", position.token_symbol, e);
                        }
                    }
                }

                // 2. Check for exits
                let positions_to_exit = self.position_manager.get_positions_to_exit().await;
                if !positions_to_exit.is_empty() {
                    println!("   🚨 Found {} positions to exit!", positions_to_exit.len());
                    for (token_mint, exit_reason) in positions_to_exit {
                        println!("      - Exiting {} due to: {:?}\n", token_mint, exit_reason);
                        match self.execute_sell_for_token(&token_mint, exit_reason).await {
                            Ok(_) => {
                                let mut sell_executed = true;
                            }
                            Err(e) => {
                                eprintln!("      ❌ Failed to execute sell for {}: {}", token_mint, e);
                            }
                        }
                    }
                }
            }
            self.position_manager.print_summary().await;
            // --- END POSITION MONITORING ---


            // --- SCAN FOR NEW TRADES ---
            let should_scan = !self.pause_listening_when_trading || !self.position_manager.has_active_positions().await;
            let mut buy_executed = false;
            let mut sell_executed = false;

            if should_scan {
                match self.scan_for_graduation_candidates().await {
                    Ok(executed_buy) => {
                        if executed_buy {
                            println!("✅ Trade executed. Immediately re-evaluating position state.");
                            buy_executed = true;
                        }
                    }
                    Err(e) => {
                        println!("⚠️  Scan for graduation candidates failed: {}", e);
                    }
                }
            } else {
                println!("\n🥚 SPERM AND EGG: Skipping new candidate scan (position active).");
            }

            // If a buy or sell was just executed, skip the sleep and immediately re-run the loop
            // to start monitoring the new position without delay.
            if buy_executed {
                println!("🔄 Restarting loop immediately after buy...");
                continue;
            }
            if sell_executed {
                println!("🔄 Restarting loop immediately after sell...");
                continue;
            }

            // Wait before next scan if no buy was executed
            println!("\n⏳ Waiting {} seconds before next scan...", scan_interval.as_secs());
            sleep(scan_interval).await;
        }

        Ok(())
    }

// Periodic scanning moved to periodic_scanner module
    async fn validate_graduation_candidate(&self, candidate: &LiveTokenData) -> bool {
        println!("🔍 VALIDATING CANDIDATE: {} ({})", candidate.name, candidate.symbol);

        // Apply existing token filters
        let trading_config = TradingConfig::from_env();
        if !crate::live_pump_integration::TokenFilters::should_trade_token(candidate, &trading_config) {
            println!("❌ Failed standard token filters");
            return false;
        }

        // Check graduation risk
        match self.check_bonding_curve_graduation(&candidate.mint).await {
            Ok(graduation_check) => {
                if graduation_check.should_sell {
                    if graduation_check.is_complete {
                        println!("🗑️  Token migrated/deleted - no longer tradeable on pump.fun");
                    } else {
                        println!("⚠️  High graduation risk ({:.1}% progress) - skipping", graduation_check.progress * 100.0);
                    }
                    return false;
                }
                println!("✅ Graduation risk acceptable ({:.1}% progress)", graduation_check.progress * 100.0);
            }
            Err(e) => {
                println!("❌ Failed to check graduation status: {}", e);
                return false;
            }
        }

        // Additional validation: Check current price stability
        match self.get_current_token_price(&candidate.mint).await {
            Ok(current_price) => {
                let price_diff = ((current_price - candidate.price_sol) / candidate.price_sol).abs();
                if price_diff > 0.1 { // 10% price change since discovery
                    println!("⚠️  Price too volatile ({:.1}% change) - skipping", price_diff * 100.0);
                    return false;
                }
                println!("✅ Price stable ({:.1}% change)", price_diff * 100.0);
            }
            Err(e) => {
                println!("❌ Failed to get current price: {}", e);
                return false;
            }
        }

        println!("✅ Candidate validation passed");
        true
    }

    /// Create and track position after successful buy
    async fn create_and_track_position(&mut self, token: &LiveTokenData, buy_result: &crate::live_pump_integration::BuyResult) -> Result<()> {
        println!("📊 ADDING POSITION TO MANAGER:");
        println!("   🪙 Token: {} ({})", token.name, token.symbol);
        println!("   💰 Amount: {} tokens", buy_result.amount_tokens);
        println!("   💵 Cost: {:.6} SOL", buy_result.amount_sol);
        println!("   📈 Entry Price: {:.9} SOL", buy_result.entry_price);

        if let Err(e) = self.position_manager.add_position(
            token.mint.clone(),
            token.symbol.clone(),
            buy_result.amount_tokens,
            buy_result.amount_sol,
            buy_result.entry_price,
        ).await {
            eprintln!("❌ Failed to add position to manager: {}", e);
            // Don't return an error, just log it. The buy was successful.
        } else {
            self.total_trades += 1;
            println!("✅ Position added to manager successfully.");
        }

        Ok(())
    }

    /// Print trading statistics
    pub fn print_trading_stats(&self) {
        println!("\n📊 LIVE TRADING STATISTICS");
        println!("==========================");
        println!("Total Trades: {}", self.total_trades);
        println!("Successful Trades: {}", self.successful_trades);
        println!("Win Rate: {:.1}%", (self.successful_trades as f64 / self.total_trades.max(1) as f64) * 100.0);
        println!("Total P&L: {:.6} SOL", self.total_pnl);

        // Print dashboard summary
        println!("\n📈 PERFORMANCE DASHBOARD");
        self.dashboard.print_dashboard();
    }
}

// GraduationCheck moved to graduation_checker module
